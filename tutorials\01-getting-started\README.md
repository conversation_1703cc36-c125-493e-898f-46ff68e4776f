# Tutorial 1: Getting Started with TypeScript

## 🎯 Learning Objectives
By the end of this tutorial, you will:
- Understand what TypeScript is and why it's useful
- Install and set up TypeScript development environment
- Write your first TypeScript program
- Understand the TypeScript compilation process
- Use basic TypeScript tooling

## 📖 What is TypeScript?

TypeScript is a **superset of JavaScript** that adds static type definitions. It was developed by Microsoft and has become one of the most popular programming languages for building large-scale applications.

### Key Benefits:
- **Type Safety**: Catch errors at compile time, not runtime
- **Better IDE Support**: Enhanced autocomplete, refactoring, and navigation
- **Modern JavaScript Features**: Use latest ES features with backward compatibility
- **Large Codebase Management**: Better organization and maintainability
- **Team Collaboration**: Self-documenting code through types

## 🛠️ Installation and Setup

### Prerequisites
- Node.js (v14 or higher)
- A code editor (VS Code recommended)

### Step 1: Install TypeScript Globally
```bash
npm install -g typescript
```

### Step 2: Verify Installation
```bash
tsc --version
```

### Step 3: Install TypeScript for VS Code
If using VS Code, install the TypeScript extension (usually pre-installed).

## 🚀 Your First TypeScript Program

### Step 1: Create a TypeScript File
Create a file named `hello.ts`:

```typescript
// hello.ts
function greet(name: string): string {
    return `Hello, ${name}!`;
}

const userName: string = "TypeScript";
console.log(greet(userName));
```

### Step 2: Compile TypeScript to JavaScript
```bash
tsc hello.ts
```

This creates a `hello.js` file:
```javascript
// hello.js
function greet(name) {
    return "Hello, " + name + "!";
}
var userName = "TypeScript";
console.log(greet(userName));
```

### Step 3: Run the JavaScript
```bash
node hello.js
```

## 🔧 TypeScript Configuration

### Creating tsconfig.json
```bash
tsc --init
```

This creates a `tsconfig.json` file with default settings. Key options:
- `target`: JavaScript version to compile to
- `module`: Module system to use
- `strict`: Enable strict type checking
- `outDir`: Output directory for compiled files

## 🎮 Hands-On Exercise

### Exercise 1: Basic Setup
1. Create a new file `calculator.ts`
2. Write a function that adds two numbers with proper types
3. Compile and run the program

### Exercise 2: Type Annotations
1. Create variables with different type annotations
2. Try assigning wrong types and see the errors
3. Fix the errors and compile successfully

## 🧪 Lab Exercise

Navigate to the `labs/beginner/01-setup/` folder and complete the exercises there.

## 🔍 Common Issues and Solutions

### Issue 1: "tsc command not found"
**Solution**: Make sure TypeScript is installed globally and your PATH includes npm global packages.

### Issue 2: Compilation Errors
**Solution**: Read the error messages carefully - TypeScript provides detailed error information.

### Issue 3: VS Code Not Recognizing TypeScript
**Solution**: Ensure you're opening a folder (not just files) and have a tsconfig.json file.

## 📚 Key Takeaways

1. TypeScript adds static typing to JavaScript
2. TypeScript files use `.ts` extension
3. TypeScript must be compiled to JavaScript before running
4. Type annotations help catch errors early
5. `tsconfig.json` configures the TypeScript compiler

## 🎯 Next Steps

In the next tutorial, we'll explore TypeScript's basic types and how to use them effectively.

## 📖 Additional Resources

- [TypeScript Official Documentation](https://www.typescriptlang.org/docs/)
- [TypeScript Playground](https://www.typescriptlang.org/play)
- [VS Code TypeScript Tutorial](https://code.visualstudio.com/docs/typescript/typescript-tutorial)
