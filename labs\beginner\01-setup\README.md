# Lab 1: Getting Started with TypeScript

## 🎯 Objectives
- Set up a TypeScript development environment
- Write and compile your first TypeScript programs
- Practice basic type annotations
- Use TypeScript compiler options

## 📋 Prerequisites
- Node.js installed (v14 or higher)
- Code editor (VS Code recommended)
- Basic JavaScript knowledge

## 🚀 Setup Instructions

1. Navigate to this lab directory
2. Install dependencies: `npm install`
3. Run the TypeScript compiler: `npm run build`
4. Run tests: `npm test`

## 🧪 Exercises

### Exercise 1: Hello TypeScript
**File**: `exercises/01-hello.ts`

Create a simple greeting program:

1. Write a function `greetUser` that takes a name (string) and returns a greeting message
2. Create a variable `userName` with your name
3. Call the function and log the result
4. Compile and run the program

**Expected Output**: "Hello, [Your Name]! Welcome to TypeScript!"

### Exercise 2: Basic Calculator
**File**: `exercises/02-calculator.ts`

Create a basic calculator with proper types:

1. Write functions for: `add`, `subtract`, `multiply`, `divide`
2. Each function should take two numbers and return a number
3. Handle division by zero (return a message)
4. Test all functions with sample values

**Requirements**:
- All parameters must be typed as `number`
- Return types must be explicitly declared
- Include error handling for division by zero

### Exercise 3: Type Annotations Practice
**File**: `exercises/03-types.ts`

Practice different type annotations:

1. Create variables with explicit types for:
   - A person's age (number)
   - Their favorite color (string)
   - Whether they have a pet (boolean)
   - Their hobbies (array of strings)
   - Their address (object with street, city, zipCode)

2. Create a function that takes these parameters and returns a formatted string

### Exercise 4: Configuration Object
**File**: `exercises/04-config.ts`

Create a configuration system:

1. Define a configuration object with:
   - `appName` (string)
   - `version` (string)
   - `debugMode` (boolean)
   - `maxUsers` (number)
   - `features` (array of strings)

2. Write a function to validate the configuration
3. Write a function to display the configuration

## 🧪 Running the Exercises

### Compile Individual Files
```bash
npx tsc exercises/01-hello.ts
node exercises/01-hello.js
```

### Compile All Files
```bash
npm run build
```

### Run Tests
```bash
npm test
```

## ✅ Success Criteria

You've successfully completed this lab when:
- [ ] All TypeScript files compile without errors
- [ ] All tests pass
- [ ] You understand basic type annotations
- [ ] You can compile and run TypeScript programs

## 🔍 Common Issues

### Issue: TypeScript not found
```bash
npm install -g typescript
```

### Issue: Compilation errors
- Check your type annotations
- Ensure all variables are properly typed
- Look for typos in function names

### Issue: Runtime errors
- Make sure you're running the compiled JavaScript files (.js)
- Check that all required parameters are provided

## 🎯 Bonus Challenges

1. **Advanced Calculator**: Add functions for power, square root, and percentage
2. **Input Validation**: Add type guards to validate input types
3. **Error Handling**: Create custom error types for different scenarios

## 📚 Key Concepts Covered

- TypeScript compilation process
- Basic type annotations
- Function parameter and return types
- Variable type declarations
- TypeScript configuration
- Error handling in TypeScript

## 🔗 Next Steps

Once you complete this lab, move on to Lab 2: Basic Types and Variables to dive deeper into TypeScript's type system.
