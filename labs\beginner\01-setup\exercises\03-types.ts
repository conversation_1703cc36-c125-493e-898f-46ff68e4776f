// Exercise 3: Type Annotations Practice
// TODO: Practice different type annotations

// TODO: Create variables with explicit type annotations for:

// TODO: A person's age (number)
// let age: number = 

// TODO: Their favorite color (string)
// let favoriteColor: string = 

// TODO: Whether they have a pet (boolean)
// let hasPet: boolean = 

// TODO: Their hobbies (array of strings)
// let hobbies: string[] = 

// TODO: Their address (object with street, city, zipCode properties)
// let address: { street: string; city: string; zipCode: string } = {
//     street: "",
//     city: "",
//     zipCode: ""
// };

// TODO: Create a function that takes these parameters and returns a formatted string
// The function should return a string like:
// "John (25) from 123 Main St, Anytown 12345 likes blue and enjoys reading, gaming"

// function createPersonDescription(
//     name: string,
//     age: number,
//     address: { street: string; city: string; zipCode: string },
//     favoriteColor: string,
//     hasPet: boolean,
//     hobbies: string[]
// ): string {
//     // Your implementation here
// }

// TODO: Test your function
// console.log(createPersonDescription(
//     "John",
//     age,
//     address,
//     favoriteColor,
//     hasPet,
//     hobbies
// ));

// Export for testing
// export { createPersonDescription };
