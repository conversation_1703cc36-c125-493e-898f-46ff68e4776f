// Test file for Lab 1 exercises
import { greetUser } from '../solutions/01-hello';
import { add, subtract, multiply, divide } from '../solutions/02-calculator';
import { createPersonDescription } from '../solutions/03-types';
import { config, validateConfig, displayConfig } from '../solutions/04-config';

describe('Exercise 1: Hello TypeScript', () => {
    test('greetUser should return correct greeting', () => {
        expect(greetUser('Alice')).toBe('Hello, Alice! Welcome to TypeScript!');
        expect(greetUser('Bob')).toBe('Hello, Bob! Welcome to TypeScript!');
    });
});

describe('Exercise 2: Basic Calculator', () => {
    test('add function should work correctly', () => {
        expect(add(5, 3)).toBe(8);
        expect(add(-2, 7)).toBe(5);
        expect(add(0, 0)).toBe(0);
    });

    test('subtract function should work correctly', () => {
        expect(subtract(10, 3)).toBe(7);
        expect(subtract(5, 8)).toBe(-3);
        expect(subtract(0, 0)).toBe(0);
    });

    test('multiply function should work correctly', () => {
        expect(multiply(4, 5)).toBe(20);
        expect(multiply(-3, 4)).toBe(-12);
        expect(multiply(0, 10)).toBe(0);
    });

    test('divide function should work correctly', () => {
        expect(divide(10, 2)).toBe(5);
        expect(divide(15, 3)).toBe(5);
        expect(divide(7, 2)).toBe(3.5);
    });

    test('divide by zero should return 0', () => {
        expect(divide(10, 0)).toBe(0);
    });
});

describe('Exercise 3: Type Annotations', () => {
    test('createPersonDescription should format correctly', () => {
        const address = { street: "123 Main St", city: "Anytown", zipCode: "12345" };
        const hobbies = ["reading", "gaming"];
        
        const result = createPersonDescription(
            "John",
            25,
            address,
            "blue",
            true,
            hobbies
        );
        
        expect(result).toContain("John (25)");
        expect(result).toContain("123 Main St, Anytown 12345");
        expect(result).toContain("blue");
        expect(result).toContain("has a pet");
        expect(result).toContain("reading, gaming");
    });
});

describe('Exercise 4: Configuration', () => {
    test('config should have all required properties', () => {
        expect(config).toHaveProperty('appName');
        expect(config).toHaveProperty('version');
        expect(config).toHaveProperty('debugMode');
        expect(config).toHaveProperty('maxUsers');
        expect(config).toHaveProperty('features');
        expect(Array.isArray(config.features)).toBe(true);
    });

    test('validateConfig should validate correctly', () => {
        expect(validateConfig(config)).toBe(true);
        
        const invalidConfig = {
            appName: "",
            version: "1.0.0",
            debugMode: true,
            maxUsers: 100,
            features: ["test"]
        };
        expect(validateConfig(invalidConfig)).toBe(false);
    });

    test('displayConfig should not throw errors', () => {
        expect(() => displayConfig(config)).not.toThrow();
    });
});
