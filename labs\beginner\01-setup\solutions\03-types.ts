// Exercise 3: Type Annotations Practice - SOLUTION

let age: number = 25;
let favoriteColor: string = "blue";
let hasPet: boolean = true;
let hobbies: string[] = ["reading", "gaming", "cooking"];
let address: { street: string; city: string; zipCode: string } = {
    street: "123 Main St",
    city: "Anytown",
    zipCode: "12345"
};

function createPersonDescription(
    name: string,
    age: number,
    address: { street: string; city: string; zipCode: string },
    favoriteColor: string,
    hasPet: boolean,
    hobbies: string[]
): string {
    const petStatus = hasPet ? "has a pet" : "doesn't have a pet";
    const hobbiesText = hobbies.join(", ");
    
    return `${name} (${age}) from ${address.street}, ${address.city} ${address.zipCode} likes ${favoriteColor}, ${petStatus}, and enjoys ${hobbiesText}`;
}

// Test the function
console.log(createPersonDescription(
    "John",
    age,
    address,
    favoriteColor,
    hasPet,
    hobbies
));

// Export for testing
export { createPersonDescription };
