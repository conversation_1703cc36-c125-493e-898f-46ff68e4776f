# Tutorial 3: Functions and Control Flow

## 🎯 Learning Objectives
By the end of this tutorial, you will:
- Write typed functions with parameters and return types
- Use optional and default parameters
- Understand function overloading
- Work with arrow functions and function expressions
- Implement control flow with proper typing

## 📖 Function Basics

### Function Declarations
```typescript
function add(a: number, b: number): number {
    return a + b;
}

function greet(name: string): string {
    return `Hello, ${name}!`;
}

function logMessage(message: string): void {
    console.log(message);
}
```

### Function Expressions
```typescript
const multiply = function(a: number, b: number): number {
    return a * b;
};

const divide = (a: number, b: number): number => {
    return a / b;
};

// Concise arrow function
const square = (x: number): number => x * x;
```

## 🔧 Parameter Types

### Optional Parameters
```typescript
function buildName(firstName: string, lastName?: string): string {
    if (lastName) {
        return `${firstName} ${lastName}`;
    }
    return firstName;
}

console.log(buildName("<PERSON>"));        // "<PERSON>"
console.log(buildName("<PERSON>", "<PERSON><PERSON>")); // "<PERSON>"
```

### Default Parameters
```typescript
function createUser(name: string, age: number = 18, isActive: boolean = true): object {
    return { name, age, isActive };
}

console.log(createUser("Alice"));                    // age: 18, isActive: true
console.log(createUser("Bob", 25));                 // age: 25, isActive: true
console.log(createUser("Charlie", 30, false));      // age: 30, isActive: false
```

### Rest Parameters
```typescript
function sum(...numbers: number[]): number {
    return numbers.reduce((total, num) => total + num, 0);
}

console.log(sum(1, 2, 3, 4, 5)); // 15

function logMessages(prefix: string, ...messages: string[]): void {
    messages.forEach(msg => console.log(`${prefix}: ${msg}`));
}
```

## 🎭 Function Overloading

```typescript
// Overload signatures
function combine(a: string, b: string): string;
function combine(a: number, b: number): number;
function combine(a: boolean, b: boolean): boolean;

// Implementation signature
function combine(a: any, b: any): any {
    return a + b;
}

// Usage
let result1 = combine("Hello", " World");  // string
let result2 = combine(5, 10);              // number
let result3 = combine(true, false);        // boolean
```

## 🏷️ Function Types

### Function Type Expressions
```typescript
type MathOperation = (a: number, b: number) => number;

const add: MathOperation = (a, b) => a + b;
const subtract: MathOperation = (a, b) => a - b;

function calculate(operation: MathOperation, x: number, y: number): number {
    return operation(x, y);
}
```

### Call Signatures
```typescript
type DescribableFunction = {
    description: string;
    (someArg: number): boolean;
};

function doSomething(fn: DescribableFunction) {
    console.log(fn.description + " returned " + fn(6));
}
```

## 🔄 Control Flow

### If Statements with Type Guards
```typescript
function processValue(value: string | number): string {
    if (typeof value === "string") {
        // TypeScript knows value is string here
        return value.toUpperCase();
    } else {
        // TypeScript knows value is number here
        return value.toString();
    }
}
```

### Switch Statements
```typescript
type Status = "pending" | "approved" | "rejected";

function handleStatus(status: Status): string {
    switch (status) {
        case "pending":
            return "Please wait for approval";
        case "approved":
            return "Your request has been approved";
        case "rejected":
            return "Your request has been rejected";
        default:
            // TypeScript ensures all cases are handled
            const exhaustiveCheck: never = status;
            return exhaustiveCheck;
    }
}
```

### Loops with Proper Typing
```typescript
// For loop
function processNumbers(numbers: number[]): number[] {
    const results: number[] = [];
    for (let i = 0; i < numbers.length; i++) {
        results.push(numbers[i] * 2);
    }
    return results;
}

// For...of loop
function sumArray(numbers: number[]): number {
    let total = 0;
    for (const num of numbers) {
        total += num;
    }
    return total;
}

// For...in loop (for object properties)
function printObjectProperties(obj: Record<string, any>): void {
    for (const key in obj) {
        console.log(`${key}: ${obj[key]}`);
    }
}
```

## 🎮 Practical Examples

### Example 1: User Validation
```typescript
interface User {
    id: number;
    name: string;
    email: string;
    age?: number;
}

function validateUser(user: User): boolean {
    if (!user.name || user.name.length < 2) {
        return false;
    }
    
    if (!user.email.includes("@")) {
        return false;
    }
    
    if (user.age !== undefined && user.age < 0) {
        return false;
    }
    
    return true;
}
```

### Example 2: Calculator Functions
```typescript
type Operation = "add" | "subtract" | "multiply" | "divide";

function calculator(a: number, b: number, operation: Operation): number {
    switch (operation) {
        case "add":
            return a + b;
        case "subtract":
            return a - b;
        case "multiply":
            return a * b;
        case "divide":
            if (b === 0) {
                throw new Error("Division by zero");
            }
            return a / b;
    }
}

// Higher-order function
function createCalculator(defaultOperation: Operation) {
    return (a: number, b: number): number => {
        return calculator(a, b, defaultOperation);
    };
}

const adder = createCalculator("add");
console.log(adder(5, 3)); // 8
```

## 🧪 Practice Exercises

### Exercise 1: Temperature Converter
Create functions to convert between Celsius and Fahrenheit:
```typescript
function celsiusToFahrenheit(celsius: number): number {
    // Your implementation
}

function fahrenheitToCelsius(fahrenheit: number): number {
    // Your implementation
}
```

### Exercise 2: Array Utilities
Create utility functions for arrays:
```typescript
function findMax(numbers: number[]): number | undefined {
    // Your implementation
}

function filterEven(numbers: number[]): number[] {
    // Your implementation
}
```

### Exercise 3: String Utilities
Create string manipulation functions:
```typescript
function capitalize(str: string): string {
    // Your implementation
}

function countWords(text: string): number {
    // Your implementation
}
```

## ❌ Common Mistakes

### 1. Missing Return Type Annotations
```typescript
// ❌ Unclear return type
function processData(data: any) {
    return data.map(item => item.value);
}

// ✅ Clear return type
function processData(data: any[]): any[] {
    return data.map(item => item.value);
}
```

### 2. Not Handling All Cases
```typescript
// ❌ Missing case
function getStatusMessage(status: "active" | "inactive" | "pending"): string {
    switch (status) {
        case "active":
            return "User is active";
        case "inactive":
            return "User is inactive";
        // Missing "pending" case
    }
}
```

## 📚 Key Takeaways

1. Always specify parameter and return types for functions
2. Use optional parameters with `?` and default parameters for flexibility
3. Function overloading provides multiple signatures for the same function
4. Type guards help TypeScript understand types in control flow
5. Exhaustive checking with `never` ensures all cases are handled

## 🎯 Next Steps

In the next tutorial, we'll explore arrays, objects, and more complex data structures.

## 🔗 Lab Exercise

Complete the exercises in `labs/beginner/03-functions/` to practice function typing and control flow!
