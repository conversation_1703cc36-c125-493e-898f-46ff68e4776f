// Exercise 1: Hello TypeScript
// TODO: Create a greeting program with proper TypeScript types

// TODO: Write a function called 'greetUser' that:
// - Takes a parameter 'name' of type string
// - Returns a string with the format: "Hello, [name]! Welcome to TypeScript!"

// TODO: Create a variable 'userName' with your name (type: string)

// TODO: Call the greetUser function with userN<PERSON> and log the result

// Starter code (remove this comment when you start):
// function greetUser(name: string): string {
//     // Your implementation here
// }

// const userName: string = "Your Name";
// console.log(greetUser(userName));
