# 🚀 Quick Start Guide - TypeScript Course

## ⚡ Get Started in 5 Minutes

### Step 1: Prerequisites Check
```bash
# Check Node.js version (should be 14+)
node --version

# Check npm version
npm --version
```

### Step 2: Install TypeScript Globally
```bash
npm install -g typescript
```

### Step 3: Verify Installation
```bash
tsc --version
```

### Step 4: Set Up the Course
```bash
# Navigate to the course directory
cd typescript-course

# Install dependencies
npm install

# Build the project
npm run build
```

### Step 5: Start Learning!
```bash
# Begin with Tutorial 1
open tutorials/01-getting-started/README.md

# Or start with hands-on coding
cd labs/beginner/01-setup
npm install
npm test
```

## 🎯 Learning Paths

### 🟢 Beginner Path (Start Here!)
1. **Tutorial 1**: Getting Started → **Lab 1**: Setup Exercises
2. **Tutorial 2**: Basic Types → Practice with type annotations
3. **Tutorial 3**: Functions → Build calculator functions
4. **Tutorial 4**: Arrays & Objects → Work with complex data
5. **Project 1**: Todo App → Apply everything learned

**Time Estimate**: 8-12 hours

### 🟡 Intermediate Path
1. **Tutorial 5**: Interfaces → Design contracts
2. **Tutorial 6**: Classes → Object-oriented programming
3. **Tutorial 7**: Modules → Code organization
4. **Tutorial 8**: Enums & Unions → Advanced types
5. **Project 2**: E-commerce Catalog → Real-world application

**Time Estimate**: 10-15 hours

### 🔴 Advanced Path
1. **Tutorial 9**: Generics → Reusable code
2. **Tutorial 10**: Utility Types → Advanced type manipulation
3. **Tutorial 11**: Decorators → Metaprogramming
4. **Tutorial 12**: Modern JavaScript → Latest features
5. **Project 3**: Task Management → Complex application

**Time Estimate**: 12-20 hours

## 🛠️ Development Setup

### VS Code Extensions (Recommended)
```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "bradlc.vscode-tailwindcss"
  ]
}
```

### Useful Commands
```bash
# Compile and watch for changes
npm run build:watch

# Run tests in watch mode
npm run test:watch

# Format code
npm run format

# Lint code
npm run lint

# Clean build directory
npm run clean
```

## 🧪 Testing Your Setup

### Quick Test
Create a file `test.ts`:
```typescript
function greet(name: string): string {
    return `Hello, ${name}!`;
}

console.log(greet("TypeScript"));
```

Compile and run:
```bash
tsc test.ts
node test.js
```

Expected output: `Hello, TypeScript!`

## 🎯 Success Milestones

### ✅ Beginner Milestones
- [ ] Successfully compile your first TypeScript file
- [ ] Complete all Lab 1 exercises
- [ ] Build a working calculator with proper types
- [ ] Create typed objects and arrays
- [ ] Start the Todo App project

### ✅ Intermediate Milestones
- [ ] Design and implement interfaces
- [ ] Create classes with inheritance
- [ ] Organize code with modules
- [ ] Use enums and union types effectively
- [ ] Complete a medium-complexity project

### ✅ Advanced Milestones
- [ ] Write generic functions and classes
- [ ] Use utility types for complex transformations
- [ ] Implement decorators
- [ ] Integrate with modern JavaScript features
- [ ] Build a production-ready application

## 🔧 Troubleshooting

### Common Issues

**Issue**: `tsc: command not found`
```bash
# Solution: Install TypeScript globally
npm install -g typescript
```

**Issue**: Compilation errors
```bash
# Check your tsconfig.json
# Verify file paths
# Check for typos in type annotations
```

**Issue**: Tests not running
```bash
# Make sure you're in the right directory
cd labs/beginner/01-setup

# Install dependencies
npm install

# Run tests
npm test
```

**Issue**: VS Code not recognizing TypeScript
```bash
# Open the folder (not just files)
# Ensure tsconfig.json exists
# Restart VS Code
```

## 📚 Additional Resources

### Official Documentation
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [TypeScript Playground](https://www.typescriptlang.org/play)

### Practice Platforms
- [TypeScript Exercises](https://typescript-exercises.github.io/)
- [Type Challenges](https://github.com/type-challenges/type-challenges)

### Community
- [TypeScript Discord](https://discord.gg/typescript)
- [r/typescript](https://reddit.com/r/typescript)

## 🎉 Ready to Start?

Choose your path:
- **New to TypeScript?** → Start with Tutorial 1
- **Want hands-on practice?** → Jump to Lab 1
- **Learn by building?** → Begin with Project 1
- **Have some experience?** → Try Tutorial 5 (Interfaces)

Happy coding! 🚀
