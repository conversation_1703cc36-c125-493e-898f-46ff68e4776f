# Tutorial 5: Interfaces and Type Aliases

## 🎯 Learning Objectives
By the end of this tutorial, you will:
- Understand the difference between interfaces and type aliases
- Create and extend interfaces effectively
- Use optional and readonly properties
- Implement function signatures in interfaces
- Work with index signatures and mapped types

## 📖 What are Interfaces?

Interfaces define the structure of objects, specifying what properties and methods an object should have. They're a powerful way to define contracts in your code.

## 🏗️ Basic Interface Syntax

### Simple Interface
```typescript
interface User {
    id: number;
    name: string;
    email: string;
    isActive: boolean;
}

let user: User = {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    isActive: true
};
```

### Optional Properties
```typescript
interface Product {
    id: number;
    name: string;
    price: number;
    description?: string;  // Optional property
    category?: string;     // Optional property
}

let product: Product = {
    id: 1,
    name: "Laptop",
    price: 999.99
    // description and category are optional
};
```

### Readonly Properties
```typescript
interface Config {
    readonly apiUrl: string;
    readonly version: string;
    timeout: number;
}

let config: Config = {
    apiUrl: "https://api.example.com",
    version: "1.0.0",
    timeout: 5000
};

// config.apiUrl = "new url"; // Error: Cannot assign to readonly property
config.timeout = 10000; // OK
```

## 🔧 Function Signatures in Interfaces

### Method Signatures
```typescript
interface Calculator {
    add(a: number, b: number): number;
    subtract(a: number, b: number): number;
    multiply(a: number, b: number): number;
    divide(a: number, b: number): number;
}

let calc: Calculator = {
    add(a, b) { return a + b; },
    subtract(a, b) { return a - b; },
    multiply(a, b) { return a * b; },
    divide(a, b) { return a / b; }
};
```

### Function Properties
```typescript
interface EventHandler {
    onClick: (event: MouseEvent) => void;
    onSubmit: (data: FormData) => Promise<void>;
    onError?: (error: Error) => void;
}

let handler: EventHandler = {
    onClick: (event) => {
        console.log("Button clicked", event);
    },
    onSubmit: async (data) => {
        // Handle form submission
        console.log("Form submitted", data);
    }
};
```

## 🔗 Interface Inheritance

### Extending Interfaces
```typescript
interface Animal {
    name: string;
    age: number;
}

interface Dog extends Animal {
    breed: string;
    bark(): void;
}

interface Cat extends Animal {
    color: string;
    meow(): void;
}

let dog: Dog = {
    name: "Buddy",
    age: 3,
    breed: "Golden Retriever",
    bark() {
        console.log("Woof!");
    }
};
```

### Multiple Inheritance
```typescript
interface Flyable {
    fly(): void;
    altitude: number;
}

interface Swimmable {
    swim(): void;
    depth: number;
}

interface Duck extends Animal, Flyable, Swimmable {
    quack(): void;
}

let duck: Duck = {
    name: "Donald",
    age: 2,
    altitude: 0,
    depth: 0,
    fly() { this.altitude = 100; },
    swim() { this.depth = 5; },
    quack() { console.log("Quack!"); }
};
```

## 📚 Index Signatures

### String Index Signatures
```typescript
interface StringDictionary {
    [key: string]: string;
}

let dict: StringDictionary = {
    "name": "John",
    "city": "New York",
    "country": "USA"
};

// Can add any string key
dict["age"] = "30"; // Must be string value
```

### Number Index Signatures
```typescript
interface NumberArray {
    [index: number]: number;
}

let numbers: NumberArray = [1, 2, 3, 4, 5];
```

### Mixed Index Signatures
```typescript
interface MixedDictionary {
    [key: string]: string | number;
    length: number; // Specific property
}

let mixed: MixedDictionary = {
    length: 3,
    "item1": "hello",
    "item2": 42,
    "item3": "world"
};
```

## 🆚 Interfaces vs Type Aliases

### Type Aliases
```typescript
type Point = {
    x: number;
    y: number;
};

type ID = string | number;

type EventCallback = (event: Event) => void;
```

### When to Use Each

**Use Interfaces when:**
- Defining object shapes that might be extended
- Creating contracts for classes to implement
- You need declaration merging

**Use Type Aliases when:**
- Creating union types
- Defining primitive type aliases
- Creating complex computed types

### Declaration Merging (Interfaces Only)
```typescript
interface Window {
    title: string;
}

interface Window {
    version: string;
}

// Both properties are now available
let window: Window = {
    title: "My App",
    version: "1.0.0"
};
```

## 🎮 Practical Examples

### Example 1: API Response Interface
```typescript
interface ApiResponse<T> {
    success: boolean;
    data: T;
    message?: string;
    errors?: string[];
    timestamp: Date;
}

interface User {
    id: number;
    username: string;
    email: string;
    profile: {
        firstName: string;
        lastName: string;
        avatar?: string;
    };
}

let userResponse: ApiResponse<User> = {
    success: true,
    data: {
        id: 1,
        username: "johndoe",
        email: "<EMAIL>",
        profile: {
            firstName: "John",
            lastName: "Doe"
        }
    },
    timestamp: new Date()
};
```

### Example 2: Database Entity Interface
```typescript
interface BaseEntity {
    id: number;
    createdAt: Date;
    updatedAt: Date;
}

interface Post extends BaseEntity {
    title: string;
    content: string;
    authorId: number;
    tags: string[];
    published: boolean;
}

interface Comment extends BaseEntity {
    postId: number;
    authorId: number;
    content: string;
    parentId?: number; // For nested comments
}

// Repository interface
interface Repository<T extends BaseEntity> {
    findById(id: number): Promise<T | null>;
    findAll(): Promise<T[]>;
    create(entity: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<T>;
    update(id: number, updates: Partial<T>): Promise<T>;
    delete(id: number): Promise<boolean>;
}
```

### Example 3: Event System
```typescript
interface EventMap {
    'user:login': { userId: number; timestamp: Date };
    'user:logout': { userId: number };
    'post:created': { postId: number; authorId: number };
    'post:updated': { postId: number; changes: string[] };
}

interface EventEmitter {
    on<K extends keyof EventMap>(event: K, listener: (data: EventMap[K]) => void): void;
    emit<K extends keyof EventMap>(event: K, data: EventMap[K]): void;
    off<K extends keyof EventMap>(event: K, listener: (data: EventMap[K]) => void): void;
}

class SimpleEventEmitter implements EventEmitter {
    private listeners: { [K in keyof EventMap]?: Array<(data: EventMap[K]) => void> } = {};

    on<K extends keyof EventMap>(event: K, listener: (data: EventMap[K]) => void): void {
        if (!this.listeners[event]) {
            this.listeners[event] = [];
        }
        this.listeners[event]!.push(listener);
    }

    emit<K extends keyof EventMap>(event: K, data: EventMap[K]): void {
        const eventListeners = this.listeners[event];
        if (eventListeners) {
            eventListeners.forEach(listener => listener(data));
        }
    }

    off<K extends keyof EventMap>(event: K, listener: (data: EventMap[K]) => void): void {
        const eventListeners = this.listeners[event];
        if (eventListeners) {
            const index = eventListeners.indexOf(listener);
            if (index > -1) {
                eventListeners.splice(index, 1);
            }
        }
    }
}
```

## 🧪 Practice Exercises

### Exercise 1: E-commerce Interfaces
Create interfaces for an e-commerce system:
```typescript
// Define interfaces for:
// - Product (with variants, pricing, inventory)
// - Customer (with address, payment methods)
// - Order (with items, shipping, payment)
// - ShoppingCart (with methods to add/remove items)
```

### Exercise 2: Social Media Platform
Create interfaces for a social media platform:
```typescript
// Define interfaces for:
// - User profile with posts and followers
// - Post with comments and likes
// - Comment system with nested replies
// - Notification system
```

## 📚 Key Takeaways

1. Interfaces define contracts for object structure
2. Use optional (`?`) and readonly properties for flexibility and immutability
3. Interfaces can extend other interfaces for code reuse
4. Index signatures allow dynamic property access
5. Choose interfaces for extensible object shapes, type aliases for unions and primitives

## 🎯 Next Steps

In the next tutorial, we'll explore classes and how they implement interfaces.

## 🔗 Lab Exercise

Complete the exercises in `labs/intermediate/05-interfaces/` to practice interface design!
