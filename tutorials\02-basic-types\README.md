# Tutorial 2: Basic Types and Variables

## 🎯 Learning Objectives
By the end of this tutorial, you will:
- Understand TypeScript's primitive types
- Use type annotations effectively
- Work with literal types and type inference
- Handle null and undefined values
- Use the `any` and `unknown` types appropriately

## 📖 TypeScript Type System

TypeScript's type system is one of its most powerful features. It helps you write more reliable code by catching errors at compile time.

## 🔢 Primitive Types

### Number
```typescript
let age: number = 25;
let price: number = 99.99;
let binary: number = 0b1010; // Binary
let octal: number = 0o744;   // Octal
let hex: number = 0xf00d;    // Hexadecimal
```

### String
```typescript
let firstName: string = "John";
let lastName: string = 'Doe';
let fullName: string = `${firstName} ${lastName}`;
let multiLine: string = `
    This is a
    multi-line string
`;
```

### Boolean
```typescript
let isActive: boolean = true;
let isComplete: boolean = false;
let hasPermission: boolean = age >= 18;
```

### Undefined and Null
```typescript
let notDefined: undefined = undefined;
let empty: null = null;

// With strict null checks
let maybeString: string | null = null;
let maybeNumber: number | undefined = undefined;
```

## 🎯 Type Annotations vs Type Inference

### Explicit Type Annotations
```typescript
let message: string = "Hello World";
let count: number = 42;
let isReady: boolean = true;
```

### Type Inference
```typescript
let message = "Hello World"; // TypeScript infers string
let count = 42;              // TypeScript infers number
let isReady = true;          // TypeScript infers boolean
```

### When to Use Each
- **Use annotations**: When TypeScript can't infer the type or for function parameters
- **Use inference**: When the type is obvious from the value

## 🏷️ Literal Types

### String Literals
```typescript
let direction: "up" | "down" | "left" | "right" = "up";
let theme: "light" | "dark" = "dark";
```

### Numeric Literals
```typescript
let diceRoll: 1 | 2 | 3 | 4 | 5 | 6 = 3;
let httpStatus: 200 | 404 | 500 = 200;
```

### Boolean Literals
```typescript
let isTrue: true = true;
let isFalse: false = false;
```

## 🌟 Special Types

### Any Type
```typescript
let anything: any = 42;
anything = "Hello";
anything = true;
anything.foo.bar; // No type checking
```

**⚠️ Warning**: Avoid `any` when possible as it disables type checking.

### Unknown Type
```typescript
let userInput: unknown = getUserInput();

// Type checking required before use
if (typeof userInput === "string") {
    console.log(userInput.toUpperCase());
}
```

### Void Type
```typescript
function logMessage(message: string): void {
    console.log(message);
    // No return value
}
```

### Never Type
```typescript
function throwError(message: string): never {
    throw new Error(message);
}

function infiniteLoop(): never {
    while (true) {
        // This function never returns
    }
}
```

## 🔄 Type Assertions

Sometimes you know more about a type than TypeScript does:

```typescript
let someValue: unknown = "Hello World";

// Angle bracket syntax
let strLength1: number = (<string>someValue).length;

// As syntax (preferred)
let strLength2: number = (someValue as string).length;
```

## 🎮 Hands-On Examples

### Example 1: User Profile
```typescript
let userId: number = 12345;
let username: string = "john_doe";
let isVerified: boolean = true;
let lastLogin: Date | null = new Date();

function displayProfile(): void {
    console.log(`User: ${username} (ID: ${userId})`);
    console.log(`Verified: ${isVerified}`);
    console.log(`Last Login: ${lastLogin?.toDateString() || "Never"}`);
}
```

### Example 2: Configuration Object
```typescript
type LogLevel = "debug" | "info" | "warn" | "error";

let logLevel: LogLevel = "info";
let maxRetries: number = 3;
let timeout: number = 5000;
let enableLogging: boolean = true;
```

## 🧪 Practice Exercises

### Exercise 1: Variable Declarations
Create variables with appropriate types for:
- A person's age
- Their favorite color
- Whether they have a driver's license
- Their salary (could be null if unemployed)

### Exercise 2: Type Inference
Declare variables without type annotations and let TypeScript infer the types:
```typescript
let productName = "Laptop";
let productPrice = 999.99;
let inStock = true;
```

### Exercise 3: Literal Types
Create a traffic light system using literal types:
```typescript
type TrafficLight = "red" | "yellow" | "green";
let currentLight: TrafficLight = "red";
```

## ❌ Common Mistakes

### 1. Overusing `any`
```typescript
// ❌ Bad
let data: any = fetchData();

// ✅ Good
let data: unknown = fetchData();
if (typeof data === "object" && data !== null) {
    // Use data safely
}
```

### 2. Unnecessary Type Annotations
```typescript
// ❌ Redundant
let message: string = "Hello";

// ✅ Better
let message = "Hello";
```

### 3. Ignoring Null/Undefined
```typescript
// ❌ Risky
function getLength(str: string | null): number {
    return str.length; // Error if str is null
}

// ✅ Safe
function getLength(str: string | null): number {
    return str?.length ?? 0;
}
```

## 📚 Key Takeaways

1. TypeScript has rich primitive types: `number`, `string`, `boolean`
2. Type inference reduces the need for explicit annotations
3. Literal types create precise type constraints
4. `unknown` is safer than `any` for dynamic content
5. Always handle `null` and `undefined` values properly

## 🎯 Next Steps

In the next tutorial, we'll explore functions, parameters, and return types in detail.

## 🔗 Lab Exercise

Complete the exercises in `labs/beginner/02-basic-types/` to practice what you've learned!
