# Tutorial 4: Arrays and Objects

## 🎯 Learning Objectives
By the end of this tutorial, you will:
- Work with typed arrays and array methods
- Create and type objects effectively
- Use object destructuring with types
- Understand tuple types and their use cases
- Work with nested data structures

## 📊 Arrays in TypeScript

### Basic Array Types
```typescript
// Array of numbers
let numbers: number[] = [1, 2, 3, 4, 5];
let moreNumbers: Array<number> = [6, 7, 8, 9, 10];

// Array of strings
let fruits: string[] = ["apple", "banana", "orange"];
let colors: Array<string> = ["red", "green", "blue"];

// Mixed type arrays (union types)
let mixed: (string | number)[] = ["hello", 42, "world", 100];
```

### Array Methods with Types
```typescript
let scores: number[] = [85, 92, 78, 96, 88];

// Map with type inference
let doubledScores: number[] = scores.map(score => score * 2);

// Filter with type safety
let highScores: number[] = scores.filter(score => score > 90);

// Reduce with explicit types
let totalScore: number = scores.reduce((sum, score) => sum + score, 0);

// Find with proper return type
let firstHighScore: number | undefined = scores.find(score => score > 90);
```

### Array Destructuring
```typescript
let coordinates: [number, number] = [10, 20];
let [x, y] = coordinates;

let names: string[] = ["Alice", "Bob", "Charlie"];
let [first, second, ...rest] = names;
```

## 🎯 Tuple Types

Tuples are arrays with fixed length and specific types for each position:

```typescript
// Basic tuple
let person: [string, number] = ["John", 30];

// Tuple with optional elements
let userInfo: [string, number, boolean?] = ["Alice", 25];

// Named tuple elements (TypeScript 4.0+)
let point: [x: number, y: number] = [10, 20];

// Readonly tuples
let readonlyTuple: readonly [string, number] = ["test", 42];
```

### Tuple Use Cases
```typescript
// Function returning multiple values
function getNameAndAge(): [string, number] {
    return ["John Doe", 30];
}

let [name, age] = getNameAndAge();

// State management (like React useState)
type State<T> = [T, (newState: T) => void];

function useState<T>(initial: T): State<T> {
    let state = initial;
    const setState = (newState: T) => {
        state = newState;
    };
    return [state, setState];
}
```

## 🏗️ Objects in TypeScript

### Object Type Annotations
```typescript
// Inline object type
let user: { name: string; age: number; email: string } = {
    name: "John Doe",
    age: 30,
    email: "<EMAIL>"
};

// Object with optional properties
let product: { 
    id: number; 
    name: string; 
    price: number; 
    description?: string 
} = {
    id: 1,
    name: "Laptop",
    price: 999.99
};
```

### Type Aliases for Objects
```typescript
type User = {
    id: number;
    name: string;
    email: string;
    isActive: boolean;
    lastLogin?: Date;
};

let currentUser: User = {
    id: 1,
    name: "Alice Smith",
    email: "<EMAIL>",
    isActive: true
};
```

### Object Methods
```typescript
type Calculator = {
    value: number;
    add: (x: number) => number;
    multiply: (x: number) => number;
    reset: () => void;
};

let calc: Calculator = {
    value: 0,
    add(x: number): number {
        this.value += x;
        return this.value;
    },
    multiply(x: number): number {
        this.value *= x;
        return this.value;
    },
    reset(): void {
        this.value = 0;
    }
};
```

### Object Destructuring
```typescript
type Person = {
    firstName: string;
    lastName: string;
    age: number;
    address: {
        street: string;
        city: string;
        zipCode: string;
    };
};

let person: Person = {
    firstName: "John",
    lastName: "Doe",
    age: 30,
    address: {
        street: "123 Main St",
        city: "Anytown",
        zipCode: "12345"
    }
};

// Destructuring with types
let { firstName, lastName, age } = person;
let { address: { city, zipCode } } = person;

// Destructuring with renaming
let { firstName: fName, lastName: lName } = person;
```

## 🔄 Working with Complex Data Structures

### Arrays of Objects
```typescript
type Student = {
    id: number;
    name: string;
    grades: number[];
    major: string;
};

let students: Student[] = [
    {
        id: 1,
        name: "Alice Johnson",
        grades: [95, 87, 92],
        major: "Computer Science"
    },
    {
        id: 2,
        name: "Bob Smith",
        grades: [78, 85, 90],
        major: "Mathematics"
    }
];

// Working with arrays of objects
function getAverageGrade(student: Student): number {
    const sum = student.grades.reduce((total, grade) => total + grade, 0);
    return sum / student.grades.length;
}

let topStudents: Student[] = students.filter(student => 
    getAverageGrade(student) > 85
);
```

### Nested Objects
```typescript
type Company = {
    name: string;
    founded: number;
    employees: {
        count: number;
        departments: {
            name: string;
            head: string;
            budget: number;
        }[];
    };
    headquarters: {
        address: string;
        country: string;
        coordinates: [number, number];
    };
};

let company: Company = {
    name: "Tech Corp",
    founded: 2010,
    employees: {
        count: 500,
        departments: [
            {
                name: "Engineering",
                head: "Jane Smith",
                budget: 2000000
            },
            {
                name: "Marketing",
                head: "John Doe",
                budget: 500000
            }
        ]
    },
    headquarters: {
        address: "123 Tech Street",
        country: "USA",
        coordinates: [40.7128, -74.0060]
    }
};
```

## 🎮 Practical Examples

### Example 1: Shopping Cart
```typescript
type Product = {
    id: number;
    name: string;
    price: number;
    category: string;
};

type CartItem = {
    product: Product;
    quantity: number;
};

type ShoppingCart = {
    items: CartItem[];
    addItem: (product: Product, quantity: number) => void;
    removeItem: (productId: number) => void;
    getTotal: () => number;
};

function createShoppingCart(): ShoppingCart {
    let items: CartItem[] = [];
    
    return {
        items,
        addItem(product: Product, quantity: number): void {
            const existingItem = items.find(item => item.product.id === product.id);
            if (existingItem) {
                existingItem.quantity += quantity;
            } else {
                items.push({ product, quantity });
            }
        },
        removeItem(productId: number): void {
            items = items.filter(item => item.product.id !== productId);
        },
        getTotal(): number {
            return items.reduce((total, item) => 
                total + (item.product.price * item.quantity), 0
            );
        }
    };
}
```

### Example 2: Data Processing
```typescript
type SalesData = {
    date: string;
    product: string;
    amount: number;
    region: string;
};

let salesRecords: SalesData[] = [
    { date: "2024-01-01", product: "Laptop", amount: 1200, region: "North" },
    { date: "2024-01-02", product: "Mouse", amount: 25, region: "South" },
    { date: "2024-01-03", product: "Laptop", amount: 1200, region: "East" }
];

function analyzeSales(records: SalesData[]): {
    totalSales: number;
    topProduct: string;
    salesByRegion: Record<string, number>;
} {
    const totalSales = records.reduce((sum, record) => sum + record.amount, 0);
    
    const productSales: Record<string, number> = {};
    const regionSales: Record<string, number> = {};
    
    records.forEach(record => {
        productSales[record.product] = (productSales[record.product] || 0) + record.amount;
        regionSales[record.region] = (regionSales[record.region] || 0) + record.amount;
    });
    
    const topProduct = Object.keys(productSales).reduce((a, b) => 
        productSales[a] > productSales[b] ? a : b
    );
    
    return {
        totalSales,
        topProduct,
        salesByRegion: regionSales
    };
}
```

## 🧪 Practice Exercises

### Exercise 1: Student Management
Create a system to manage student records:
```typescript
type Grade = {
    subject: string;
    score: number;
    date: string;
};

type Student = {
    id: number;
    name: string;
    grades: Grade[];
};

// Implement these functions:
function addGrade(student: Student, grade: Grade): void {
    // Your implementation
}

function getAverageScore(student: Student): number {
    // Your implementation
}

function getGradesBySubject(student: Student, subject: string): Grade[] {
    // Your implementation
}
```

### Exercise 2: Inventory System
Create an inventory management system:
```typescript
type InventoryItem = {
    id: number;
    name: string;
    quantity: number;
    price: number;
    category: string;
};

// Implement these functions:
function addInventoryItem(inventory: InventoryItem[], item: InventoryItem): void {
    // Your implementation
}

function updateQuantity(inventory: InventoryItem[], id: number, newQuantity: number): boolean {
    // Your implementation
}

function getItemsByCategory(inventory: InventoryItem[], category: string): InventoryItem[] {
    // Your implementation
}
```

## 📚 Key Takeaways

1. Arrays in TypeScript are strongly typed and support all JavaScript array methods
2. Tuples provide fixed-length arrays with specific types for each position
3. Objects can be typed inline or with type aliases for reusability
4. Destructuring works seamlessly with TypeScript's type system
5. Complex nested structures maintain type safety throughout

## 🎯 Next Steps

Congratulations! You've completed the beginner level tutorials. Next, we'll move to intermediate topics starting with interfaces and type aliases.

## 🔗 Lab Exercise

Complete the exercises in `labs/beginner/04-arrays-objects/` to practice working with complex data structures!
