# Project 1: Todo List Application (Beginner)

## 🎯 Project Overview
Build a complete Todo List application using TypeScript to practice fundamental concepts including types, interfaces, functions, and basic object-oriented programming.

## 📚 Concepts Covered
- Basic TypeScript types and interfaces
- Function typing and implementation
- Array manipulation with types
- Object-oriented programming basics
- Error handling and validation
- Local storage integration

## 🏗️ Project Structure
```
todo-app/
├── src/
│   ├── models/
│   │   ├── Todo.ts
│   │   └── TodoList.ts
│   ├── services/
│   │   └── StorageService.ts
│   ├── utils/
│   │   └── validators.ts
│   └── app.ts
├── tests/
│   ├── Todo.test.ts
│   ├── TodoList.test.ts
│   └── StorageService.test.ts
├── package.json
├── tsconfig.json
└── README.md
```

## 🎯 Requirements

### Core Features
1. **Add Todo**: Create new todo items with title and description
2. **List Todos**: Display all todos with their status
3. **Toggle Status**: Mark todos as complete/incomplete
4. **Delete Todo**: Remove todos from the list
5. **Filter Todos**: Show all, completed, or pending todos
6. **Persist Data**: Save todos to local storage

### Data Model
```typescript
interface Todo {
    id: number;
    title: string;
    description?: string;
    completed: boolean;
    createdAt: Date;
    completedAt?: Date;
}

interface TodoFilter {
    status: 'all' | 'completed' | 'pending';
    searchTerm?: string;
}
```

## 🚀 Getting Started

### Step 1: Setup
```bash
cd projects/todo-app
npm install
npm run build
npm test
```

### Step 2: Implementation Plan
1. Create the Todo interface and basic types
2. Implement the TodoList class with CRUD operations
3. Add validation utilities
4. Implement storage service for persistence
5. Create the main application logic
6. Add comprehensive tests

### Step 3: Run the Application
```bash
npm run dev
```

## 🧪 Implementation Tasks

### Task 1: Define Types and Interfaces
**File**: `src/models/Todo.ts`
- Define the Todo interface
- Create TodoStatus enum
- Add TodoFilter interface

### Task 2: Implement TodoList Class
**File**: `src/models/TodoList.ts`
- Create TodoList class with proper typing
- Implement add, remove, toggle, and filter methods
- Add validation for operations

### Task 3: Storage Service
**File**: `src/services/StorageService.ts`
- Implement local storage operations
- Add error handling for storage failures
- Ensure type safety for stored data

### Task 4: Validation Utilities
**File**: `src/utils/validators.ts`
- Create validation functions for todo data
- Implement input sanitization
- Add error message generation

### Task 5: Main Application
**File**: `src/app.ts`
- Integrate all components
- Implement user interface logic
- Add error handling and user feedback

## 🧪 Testing Requirements

### Unit Tests
- Test all TodoList methods
- Validate storage operations
- Test validation functions
- Ensure error handling works correctly

### Integration Tests
- Test complete workflows
- Validate data persistence
- Test filter and search functionality

## 🎯 Success Criteria

You've successfully completed this project when:
- [ ] All TypeScript code compiles without errors
- [ ] All unit tests pass
- [ ] Application runs without runtime errors
- [ ] Todos persist between sessions
- [ ] All CRUD operations work correctly
- [ ] Filtering and search functionality works
- [ ] Code follows TypeScript best practices

## 🔧 Bonus Features

1. **Due Dates**: Add due date functionality with reminders
2. **Categories**: Organize todos into categories
3. **Priority Levels**: Add high, medium, low priority
4. **Export/Import**: Save todos to/from JSON files
5. **Statistics**: Show completion rates and productivity metrics

## 📚 Learning Outcomes

After completing this project, you will understand:
- How to design TypeScript interfaces for real applications
- Proper error handling and validation in TypeScript
- Working with dates and time in TypeScript
- Local storage integration with type safety
- Testing TypeScript applications
- Object-oriented programming patterns in TypeScript

## 🔗 Next Steps

Once you complete this project:
1. Review your code for TypeScript best practices
2. Add additional features from the bonus list
3. Move on to Project 2: E-commerce Product Catalog (Intermediate)
4. Share your implementation for code review

## 📖 Resources

- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Jest Testing Framework](https://jestjs.io/docs/getting-started)
- [Local Storage API](https://developer.mozilla.org/en-US/docs/Web/API/Window/localStorage)
