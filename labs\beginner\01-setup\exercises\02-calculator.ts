// Exercise 2: Basic Calculator
// TODO: Create a calculator with proper TypeScript types

// TODO: Implement the following functions with proper type annotations:

// TODO: add function - takes two numbers, returns their sum
// function add(a: number, b: number): number {
//     // Your implementation here
// }

// TODO: subtract function - takes two numbers, returns their difference
// function subtract(a: number, b: number): number {
//     // Your implementation here
// }

// TODO: multiply function - takes two numbers, returns their product
// function multiply(a: number, b: number): number {
//     // Your implementation here
// }

// TODO: divide function - takes two numbers, returns their quotient
// <PERSON>le division by zero by returning 0 and logging an error message
// function divide(a: number, b: number): number {
//     // Your implementation here
//     // Remember to check if b === 0
// }

// TODO: Test your functions with these values:
// console.log("Addition: 10 + 5 =", add(10, 5));
// console.log("Subtraction: 10 - 5 =", subtract(10, 5));
// console.log("Multiplication: 10 * 5 =", multiply(10, 5));
// console.log("Division: 10 / 5 =", divide(10, 5));
// console.log("Division by zero: 10 / 0 =", divide(10, 0));

// Export functions for testing
// export { add, subtract, multiply, divide };
