# TypeScript Course Troubleshooting Guide

## 🔧 Installation Issues

### TypeScript Not Found
**Error**: `tsc: command not found` or `'tsc' is not recognized`

**Solutions**:
```bash
# Install TypeScript globally
npm install -g typescript

# Verify installation
tsc --version

# If still not working, check PATH
echo $PATH  # Linux/Mac
echo %PATH%  # Windows

# Alternative: Use npx
npx tsc --version
```

### Node.js Version Issues
**Error**: TypeScript requires Node.js version 14 or higher

**Solutions**:
```bash
# Check current version
node --version

# Update Node.js
# Visit https://nodejs.org and download latest LTS
# Or use a version manager like nvm
```

## 🏗️ Compilation Errors

### Type Errors
**Error**: `Type 'string' is not assignable to type 'number'`

**Solutions**:
```typescript
// ❌ Wrong
let age: number = "25";

// ✅ Correct
let age: number = 25;
let ageString: string = "25";
let ageFromString: number = parseInt("25");
```

### Missing Type Declarations
**Error**: `Could not find a declaration file for module 'some-package'`

**Solutions**:
```bash
# Install type definitions
npm install @types/node
npm install @types/jest
npm install @types/express  # for Express.js

# Or ignore the error (not recommended)
// @ts-ignore
```

### Strict Mode Errors
**Error**: `Object is possibly 'null'` or `Object is possibly 'undefined'`

**Solutions**:
```typescript
// ❌ Risky
function getLength(str: string | null): number {
    return str.length;  // Error: str might be null
}

// ✅ Safe options
function getLength(str: string | null): number {
    // Option 1: Type guard
    if (str === null) {
        return 0;
    }
    return str.length;
    
    // Option 2: Optional chaining
    return str?.length ?? 0;
    
    // Option 3: Non-null assertion (use carefully)
    return str!.length;
}
```

## 🧪 Testing Issues

### Jest Configuration
**Error**: Jest doesn't recognize TypeScript files

**Solution**: Ensure proper Jest configuration in `package.json`:
```json
{
  "jest": {
    "preset": "ts-jest",
    "testEnvironment": "node",
    "testMatch": ["**/__tests__/**/*.test.ts"]
  }
}
```

### Import/Export Issues in Tests
**Error**: `Cannot use import statement outside a module`

**Solutions**:
```typescript
// ✅ Use CommonJS in test files
const { myFunction } = require('../src/myModule');

// ✅ Or configure Jest for ES modules
// In package.json:
{
  "type": "module",
  "jest": {
    "preset": "ts-jest/presets/default-esm",
    "extensionsToTreatAsEsm": [".ts"]
  }
}
```

## 🎯 VS Code Issues

### TypeScript Not Working in VS Code
**Symptoms**: No IntelliSense, no error highlighting

**Solutions**:
1. **Check TypeScript version**:
   - Press `Ctrl+Shift+P` (Cmd+Shift+P on Mac)
   - Type "TypeScript: Select TypeScript Version"
   - Choose "Use Workspace Version" or "Use VS Code's Version"

2. **Restart TypeScript service**:
   - Press `Ctrl+Shift+P`
   - Type "TypeScript: Restart TS Server"

3. **Check file associations**:
   - Ensure `.ts` files are associated with TypeScript
   - Check the language mode in the bottom right corner

### Workspace Configuration
**Issue**: TypeScript settings not applying

**Solution**: Create `.vscode/settings.json`:
```json
{
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  }
}
```

## 📁 Project Structure Issues

### Module Resolution
**Error**: `Cannot find module './myModule'`

**Solutions**:
```typescript
// ✅ Correct relative imports
import { myFunction } from './myModule';      // Same directory
import { utils } from '../utils/helpers';    // Parent directory
import { config } from '../../config';       // Two levels up

// ✅ Check file extensions
import { data } from './data.json';          // JSON files
import { Component } from './Component';     // .ts/.tsx files (no extension needed)
```

### Path Mapping Issues
**Error**: Absolute imports not working

**Solution**: Configure `tsconfig.json`:
```json
{
  "compilerOptions": {
    "baseUrl": "./",
    "paths": {
      "@/*": ["src/*"],
      "@utils/*": ["src/utils/*"],
      "@components/*": ["src/components/*"]
    }
  }
}
```

## 🔄 Build Issues

### Output Directory Problems
**Error**: Compiled files in wrong location

**Solution**: Check `tsconfig.json`:
```json
{
  "compilerOptions": {
    "outDir": "./dist",
    "rootDir": "./src"
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
```

### Source Maps Not Working
**Issue**: Can't debug TypeScript in browser/Node.js

**Solution**: Enable source maps:
```json
{
  "compilerOptions": {
    "sourceMap": true,
    "inlineSourceMap": false,
    "sourceRoot": "/"
  }
}
```

## 🚀 Runtime Issues

### Type Errors at Runtime
**Issue**: TypeScript compiles but fails at runtime

**Common Causes**:
1. **Type assertions bypassing checks**:
```typescript
// ❌ Dangerous
const user = data as User;  // data might not actually be a User

// ✅ Better
function isUser(obj: any): obj is User {
    return obj && typeof obj.name === 'string' && typeof obj.id === 'number';
}

if (isUser(data)) {
    // Now data is safely typed as User
}
```

2. **External data not matching types**:
```typescript
// ✅ Validate API responses
interface ApiUser {
    id: number;
    name: string;
}

function validateUser(data: unknown): ApiUser {
    if (typeof data === 'object' && data !== null) {
        const obj = data as any;
        if (typeof obj.id === 'number' && typeof obj.name === 'string') {
            return obj as ApiUser;
        }
    }
    throw new Error('Invalid user data');
}
```

## 📚 Learning Issues

### Overwhelming Type Errors
**Problem**: Too many type errors when starting

**Solutions**:
1. **Start with gradual typing**:
```typescript
// Start with any, then gradually add types
let data: any = fetchData();

// Later, add proper types
interface UserData {
    id: number;
    name: string;
}
let data: UserData = fetchData();
```

2. **Use TypeScript's strict mode gradually**:
```json
{
  "compilerOptions": {
    "strict": false,           // Start here
    "noImplicitAny": true,     // Add this first
    "strictNullChecks": true,  // Then this
    "strict": true             // Finally enable all
  }
}
```

### Complex Type Definitions
**Problem**: Advanced types are confusing

**Approach**:
1. Master basics first (primitives, objects, arrays)
2. Learn interfaces and type aliases
3. Understand union and intersection types
4. Practice with generics
5. Explore utility types
6. Study advanced patterns

## 🆘 Getting Help

### Official Resources
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [TypeScript GitHub Issues](https://github.com/microsoft/TypeScript/issues)
- [TypeScript Playground](https://www.typescriptlang.org/play)

### Community Support
- [Stack Overflow](https://stackoverflow.com/questions/tagged/typescript)
- [TypeScript Discord](https://discord.gg/typescript)
- [Reddit r/typescript](https://reddit.com/r/typescript)

### Debugging Tips
1. **Read error messages carefully** - TypeScript provides detailed information
2. **Use the TypeScript playground** to test small code snippets
3. **Check the compiled JavaScript** to understand what TypeScript generates
4. **Use console.log with type annotations** to debug type issues
5. **Start simple** and gradually add complexity

Remember: TypeScript is designed to help you write better code. The errors are there to catch problems early!
