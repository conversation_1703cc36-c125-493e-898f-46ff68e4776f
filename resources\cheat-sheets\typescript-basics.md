# TypeScript Basics Cheat Sheet

## 🔢 Basic Types

```typescript
// Primitives
let age: number = 25;
let name: string = "<PERSON>";
let isActive: boolean = true;

// Arrays
let numbers: number[] = [1, 2, 3];
let names: Array<string> = ["<PERSON>", "<PERSON>"];

// Tuples
let person: [string, number] = ["<PERSON>", 30];

// Any (avoid when possible)
let anything: any = "could be anything";

// Unknown (safer than any)
let userInput: unknown = getUserInput();

// Void (no return value)
function logMessage(): void {
    console.log("Hello");
}

// Never (never returns)
function throwError(): never {
    throw new Error("Something went wrong");
}
```

## 🏗️ Objects and Interfaces

```typescript
// Object type
let user: { name: string; age: number } = {
    name: "<PERSON>",
    age: 30
};

// Interface
interface User {
    id: number;
    name: string;
    email?: string;  // Optional property
    readonly created: Date;  // Readonly property
}

// Extending interfaces
interface Admin extends User {
    permissions: string[];
}

// Index signatures
interface StringDictionary {
    [key: string]: string;
}
```

## 🔧 Functions

```typescript
// Function declaration
function add(a: number, b: number): number {
    return a + b;
}

// Arrow function
const multiply = (a: number, b: number): number => a * b;

// Optional parameters
function greet(name: string, greeting?: string): string {
    return `${greeting || "Hello"}, ${name}!`;
}

// Default parameters
function createUser(name: string, age: number = 18): User {
    return { name, age };
}

// Rest parameters
function sum(...numbers: number[]): number {
    return numbers.reduce((total, num) => total + num, 0);
}

// Function type
type MathOperation = (a: number, b: number) => number;
const divide: MathOperation = (a, b) => a / b;
```

## 🎭 Union and Literal Types

```typescript
// Union types
let id: string | number = "abc123";
id = 123;  // Also valid

// Literal types
let direction: "up" | "down" | "left" | "right" = "up";
let status: "loading" | "success" | "error" = "loading";

// Type guards
function processId(id: string | number): string {
    if (typeof id === "string") {
        return id.toUpperCase();
    } else {
        return id.toString();
    }
}
```

## 🏷️ Type Aliases

```typescript
// Basic type alias
type ID = string | number;
type User = {
    id: ID;
    name: string;
    email: string;
};

// Function type alias
type EventHandler = (event: Event) => void;

// Generic type alias
type ApiResponse<T> = {
    data: T;
    success: boolean;
    message?: string;
};
```

## 🔄 Enums

```typescript
// Numeric enum
enum Direction {
    Up,    // 0
    Down,  // 1
    Left,  // 2
    Right  // 3
}

// String enum
enum Color {
    Red = "red",
    Green = "green",
    Blue = "blue"
}

// Usage
let userDirection: Direction = Direction.Up;
let favoriteColor: Color = Color.Blue;
```

## 🎯 Classes

```typescript
class Animal {
    protected name: string;
    
    constructor(name: string) {
        this.name = name;
    }
    
    public speak(): void {
        console.log(`${this.name} makes a sound`);
    }
}

class Dog extends Animal {
    private breed: string;
    
    constructor(name: string, breed: string) {
        super(name);
        this.breed = breed;
    }
    
    public speak(): void {
        console.log(`${this.name} barks`);
    }
    
    public getBreed(): string {
        return this.breed;
    }
}

// Implementing interfaces
interface Flyable {
    fly(): void;
}

class Bird implements Flyable {
    fly(): void {
        console.log("Flying high!");
    }
}
```

## 🔍 Type Assertions

```typescript
// Angle bracket syntax
let someValue: unknown = "hello world";
let strLength: number = (<string>someValue).length;

// As syntax (preferred)
let strLength2: number = (someValue as string).length;

// Non-null assertion
let element: HTMLElement | null = document.getElementById("myElement");
let elementHeight: number = element!.offsetHeight;  // ! asserts non-null
```

## 🛡️ Utility Types

```typescript
interface User {
    id: number;
    name: string;
    email: string;
    age: number;
}

// Partial - makes all properties optional
type PartialUser = Partial<User>;

// Required - makes all properties required
type RequiredUser = Required<User>;

// Pick - select specific properties
type UserSummary = Pick<User, "id" | "name">;

// Omit - exclude specific properties
type CreateUser = Omit<User, "id">;

// Record - create object type with specific keys and values
type UserRoles = Record<string, "admin" | "user" | "guest">;
```

## 🔧 Common Patterns

```typescript
// Optional chaining
user?.profile?.address?.street;

// Nullish coalescing
const username = user.name ?? "Anonymous";

// Type guards
function isString(value: unknown): value is string {
    return typeof value === "string";
}

// Discriminated unions
type Shape = 
    | { kind: "circle"; radius: number }
    | { kind: "rectangle"; width: number; height: number };

function getArea(shape: Shape): number {
    switch (shape.kind) {
        case "circle":
            return Math.PI * shape.radius ** 2;
        case "rectangle":
            return shape.width * shape.height;
    }
}
```

## ⚡ Quick Tips

1. **Use `unknown` instead of `any`** when you don't know the type
2. **Enable strict mode** in tsconfig.json for better type safety
3. **Use type assertions sparingly** - prefer type guards
4. **Prefer interfaces for object shapes** that might be extended
5. **Use type aliases for unions** and complex types
6. **Always handle null/undefined** with optional chaining or guards
7. **Use const assertions** for immutable data: `as const`
8. **Leverage utility types** instead of writing complex types manually

## 🚨 Common Mistakes to Avoid

- Using `any` everywhere (defeats the purpose of TypeScript)
- Ignoring compiler errors (they're there for a reason!)
- Not handling null/undefined values
- Overusing type assertions
- Not using strict mode
- Mixing up interfaces and type aliases inappropriately
