{"name": "typescript-full-course", "version": "1.0.0", "description": "Complete TypeScript course from beginner to advanced with tutorials, labs, and projects", "main": "index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "dev": "ts-node-dev --respawn --transpile-only", "lint": "eslint . --ext .ts", "format": "prettier --write \"**/*.{ts,js,json,md}\"", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["typescript", "tutorial", "course", "beginner", "advanced", "coding-labs", "exercises"], "author": "TypeScript Course", "license": "MIT", "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "rimraf": "^5.0.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "ts-node-dev": "^2.0.0", "typescript": "^5.0.0"}, "dependencies": {}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "testMatch": ["**/__tests__/**/*.test.ts", "**/tests/**/*.test.ts"]}}