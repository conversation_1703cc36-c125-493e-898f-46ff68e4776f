// Exercise 4: Configuration Object
// TODO: Create a configuration system with proper types

// TODO: Define a configuration object with the following properties:
// - appName (string)
// - version (string) 
// - debugMode (boolean)
// - maxUsers (number)
// - features (array of strings)

// let config: {
//     appName: string;
//     version: string;
//     debugMode: boolean;
//     maxUsers: number;
//     features: string[];
// } = {
//     // Your values here
// };

// TODO: Write a function to validate the configuration
// The function should check:
// - appName is not empty
// - version follows format "x.y.z" (simple check for dots)
// - maxUsers is positive
// - features array is not empty

// function validateConfig(config: {
//     appName: string;
//     version: string;
//     debugMode: boolean;
//     maxUsers: number;
//     features: string[];
// }): boolean {
//     // Your implementation here
//     // Return true if valid, false otherwise
// }

// TODO: Write a function to display the configuration
// function displayConfig(config: {
//     appName: string;
//     version: string;
//     debugMode: boolean;
//     maxUsers: number;
//     features: string[];
// }): void {
//     // Your implementation here
//     // Print each configuration value in a readable format
// }

// TODO: Test your functions
// console.log("Configuration is valid:", validateConfig(config));
// displayConfig(config);

// Export for testing
// export { config, validateConfig, displayConfig };
