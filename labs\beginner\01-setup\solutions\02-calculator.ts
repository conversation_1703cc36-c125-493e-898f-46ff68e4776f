// Exercise 2: Basic Calculator - SOLUTION

function add(a: number, b: number): number {
    return a + b;
}

function subtract(a: number, b: number): number {
    return a - b;
}

function multiply(a: number, b: number): number {
    return a * b;
}

function divide(a: number, b: number): number {
    if (b === 0) {
        console.error("Error: Division by zero is not allowed");
        return 0;
    }
    return a / b;
}

// Test the functions
console.log("Addition: 10 + 5 =", add(10, 5));
console.log("Subtraction: 10 - 5 =", subtract(10, 5));
console.log("Multiplication: 10 * 5 =", multiply(10, 5));
console.log("Division: 10 / 5 =", divide(10, 5));
console.log("Division by zero: 10 / 0 =", divide(10, 0));

// Export functions for testing
export { add, subtract, multiply, divide };
