// Exercise 4: Configuration Object - SOLUTION

let config: {
    appName: string;
    version: string;
    debugMode: boolean;
    maxUsers: number;
    features: string[];
} = {
    appName: "TypeScript Learning App",
    version: "1.0.0",
    debugMode: true,
    maxUsers: 1000,
    features: ["authentication", "user-management", "reporting", "analytics"]
};

function validateConfig(config: {
    appName: string;
    version: string;
    debugMode: boolean;
    maxUsers: number;
    features: string[];
}): boolean {
    // Check if appName is not empty
    if (!config.appName || config.appName.trim().length === 0) {
        console.error("Validation failed: appName cannot be empty");
        return false;
    }
    
    // Check version format (simple check for dots)
    if (!config.version.includes(".")) {
        console.error("Validation failed: version should follow x.y.z format");
        return false;
    }
    
    // Check maxUsers is positive
    if (config.maxUsers <= 0) {
        console.error("Validation failed: maxUsers must be positive");
        return false;
    }
    
    // Check features array is not empty
    if (config.features.length === 0) {
        console.error("Validation failed: features array cannot be empty");
        return false;
    }
    
    return true;
}

function displayConfig(config: {
    appName: string;
    version: string;
    debugMode: boolean;
    maxUsers: number;
    features: string[];
}): void {
    console.log("=== Application Configuration ===");
    console.log(`App Name: ${config.appName}`);
    console.log(`Version: ${config.version}`);
    console.log(`Debug Mode: ${config.debugMode ? "Enabled" : "Disabled"}`);
    console.log(`Max Users: ${config.maxUsers}`);
    console.log(`Features: ${config.features.join(", ")}`);
    console.log("================================");
}

// Test the functions
console.log("Configuration is valid:", validateConfig(config));
displayConfig(config);

// Export for testing
export { config, validateConfig, displayConfig };
